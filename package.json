{"name": "game_api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:init:from:empty": "npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script > prisma/migrations/0_init/migration.sql", "db:resolve:init": "npx prisma migrate resolve --applied 0_init", "db:seed": "ts-node prisma/seed/index.ts", "db:generate": "./node_modules/.bin/prisma generate", "db:migrate:create": "./node_modules/.bin/prisma migrate dev --create-only", "db:migrate:dev": "./node_modules/.bin/prisma migrate dev", "db:migrate:prod": "./node_modules/.bin/prisma migrate deploy", "db:reset:dev": "./node_modules/.bin/prisma migrate reset && npm run db:migrate:dev && npm run db:seed", "db:reverse:eng": "npx prisma db pull", "gen:single:module": "nest g mo ", "gen:service": "nest g s ", "gen:controller": "nest g co ", "gen:module": "./scripts/gen-module.sh", "gen:command:handlers": "./scripts/gen-command-handlers.sh"}, "dependencies": {"@aws-sdk/client-apigatewaymanagementapi": "^3.637.0", "@aws-sdk/client-sqs": "^3.637.0", "@nestjs/bullmq": "^10.2.1", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/cqrs": "^10.2.7", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@prisma/client": "^5.19.0", "@sentry/node": "^8.26.0", "aws-sdk": "^2.1682.0", "axios": "^1.7.5", "bullmq": "^5.21.2", "dotenv": "^16.4.5", "firebase-admin": "^12.3.1", "jwt-decode": "^4.0.0", "node-fetch": "^3.3.2", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "uuid": "^10.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.18.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}