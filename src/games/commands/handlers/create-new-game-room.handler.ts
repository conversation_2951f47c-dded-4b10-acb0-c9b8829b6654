import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHand<PERSON> } from '@nestjs/cqrs';
import { CreateNewGameRoomCommand } from '../create-new-game-room.command';
import { v4 as uuidv4 } from 'uuid';
import GameModeEnumerator from 'src/games/models/enumerators/game-mode.enumerator';
import GameConfigParamsModel from 'src/shared/models/game-config-params.model';
import { GameStateEnumerator } from '@prisma/client';
import { QuestionsModel } from 'src/questions/models/questions.model';
import { PrismaService } from 'src/prisma/prisma.service';
import { RoomsModel } from 'src/rooms/models/rooms.model';
import showDebugConsole from 'src/shared/utils/show-debug-console';

@CommandHandler(CreateNewGameRoomCommand)
export class CreateNewGameRoomHandler
  implements ICommandHandler<CreateNewGameRoomCommand>
{
  maxPlayers = {
    singlePlayer: 1,
    fourRandom: 4,
    oneOnInvitedPlayers: 4,
    spotMatch: 4,
  };

  constructor(private readonly prismaService: PrismaService) {}

  async execute(command: CreateNewGameRoomCommand): Promise<string> {
    const room = this.createRoom({
      gameMode: command.gameMode,
      remoteConfigData: command.remoteConfigData,
      quizId: command.quizId,
      questions: command.questions,
      roomName: command.roomName,
      spotName: command.spotName,
    });
    await this.prismaService.games.create({
      data: {
        name: room.roomName,
        round: 0,
        status: 'OPENED',
        state: GameStateEnumerator.waitingPlayers,
        data: JSON.stringify(room),
        game_mode: command.gameMode as any,
        max_users: this.maxPlayers[command.gameMode],
        quiz_id: command.quizId,
      },
    });

    showDebugConsole('creating a new game: room: ' + JSON.stringify(room));
    return room.roomName;
  }

  private createRoom({
    roomName,
    gameMode,
    quizId,
    remoteConfigData,
    questions,
    spotName,
  }: {
    roomName?: string;
    quizId: number;
    gameMode: GameModeEnumerator;
    remoteConfigData: GameConfigParamsModel;
    questions: QuestionsModel[];
    spotName?: string;
  }): RoomsModel {
    let localRoomName = roomName;

    if (!localRoomName) {
      if (gameMode === GameModeEnumerator.spotMatch && spotName) {
        localRoomName = `${spotName}--${uuidv4()}`;
      } else {
        localRoomName = `room-${
          gameMode == GameModeEnumerator.oneOnInvitedPlayers ? 'private' : ''
        }-${uuidv4()}-${quizId}`;
      }
    }

    return {
      roomName: localRoomName,
      timeAnsweringRound:
        gameMode == GameModeEnumerator.singlePlayer
          ? remoteConfigData.timeAnsweringRoundSingleplayer
          : remoteConfigData.timeAnsweringRound,
      timeReviewingRound:
        gameMode == GameModeEnumerator.singlePlayer
          ? remoteConfigData.timeReviewingRoundSingleplayer
          : remoteConfigData.timeReviewingRound,
      timeAnsweringQuestionDirect: remoteConfigData.timeAnsweringQuestionDirect,
      timeAnsweringQuestionMultiple:
        gameMode == GameModeEnumerator.singlePlayer
          ? remoteConfigData.timeAnsweringRoundSingleplayer
          : remoteConfigData.timeAnsweringRound,
      timeWaitingStartingGame:
        gameMode == GameModeEnumerator.singlePlayer
          ? remoteConfigData.timeWaitingStartGameSingleplayer
          : remoteConfigData.timeWaitingStartGameMultiplayer,
      questions,
      numRounds: questions.length,
      flagCurrentRound: 0,
      lastStatusChange: Date.now(),
      stateTimeInSeconds: remoteConfigData.timeWaitingStartingGame,
      flagPlayersFinished: 0,
      flagPlayersReady: 0, // deprecated
      flagMaxPlayers: this.maxPlayers[gameMode],
      answers: [],
      users: [],
      pontuations: [],
      gameMode: gameMode,
      gameState: GameStateEnumerator.waitingPlayers,
      quiz_id: quizId,
    } as RoomsModel;
  }
}
