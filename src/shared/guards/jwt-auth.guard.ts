import { Injectable, ExecutionContext } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(protected readonly reflector: Reflector) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const skipJwtAuth = this.reflector.get<boolean>(
      'skipJwtAuth',
      context.getHandler(),
    );

    if (skipJwtAuth) {
      return true;
    }

    return super.canActivate(context) as Promise<boolean>;
  }
}
