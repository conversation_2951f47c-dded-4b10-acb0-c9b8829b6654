import { Command<PERSON>and<PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CancelEventCommand } from '../cancel-event.command';
import * as AWS from 'aws-sdk';

@CommandHandler(CancelEventCommand)
export class CancelEventHandler implements ICommandHandler<CancelEventCommand> {
  async execute(command: CancelEventCommand): Promise<void> {
    const stepFunctions = new AWS.StepFunctions();

    try {
      const result = await stepFunctions
        .stopExecution({
          executionArn: command.eventId,
          cause: 'Execution canceled by system',
        })
        .promise();

      console.log('Step Function execution canceled successfully:', result);
    } catch (error) {
      console.error('Failed to cancel Step Function execution:', error);
      throw error;
    }
  }
}
