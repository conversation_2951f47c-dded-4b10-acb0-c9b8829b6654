import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { GamesModule } from './games/games.module';
import { GameUsersModule } from './game-users/game-users.module';
import { QuestionsModule } from './questions/questions.module';
import { GameEventsModule } from './game-events/game-events.module';
import { BotsModule } from './bots/bots.module';
import { RoomsModule } from './rooms/rooms.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { ChatModule } from './chat/chat.module';
import { BullModule } from '@nestjs/bullmq';
import showDebugConsole from './shared/utils/show-debug-console';
const port = Number(process.env.REDIS_PORT || 0);
const dbId = process.env.REDIS_DB_ID ? Number(process.env.REDIS_DB_ID) : 0;
const redisTls = undefined;

showDebugConsole('db: ' + process.env.DATABASE_URL);
@Module({
  imports: [
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST,
        port: port,
        password: process.env.REDIS_PASS,
        db: dbId,
        tls: redisTls,
      },
    }),
    PrismaModule,
    GamesModule,
    GameUsersModule,
    QuestionsModule,
    GameEventsModule,
    BotsModule,
    RoomsModule,
    SchedulerModule,
    ChatModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
