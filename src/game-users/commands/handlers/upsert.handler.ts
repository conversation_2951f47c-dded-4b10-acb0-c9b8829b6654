import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { UpsertCommand } from '../upsert.command';
import { PrismaService } from 'src/prisma/prisma.service';
import GameUserEntity from 'src/game-users/models/entities/game-user.entity';

@CommandHandler(UpsertCommand)
export class UpsertHandler implements ICommandHandler<UpsertCommand> {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(command: UpsertCommand): Promise<GameUserEntity> {
    const { id, ...userData } = command.data;
    let gameUser: GameUserEntity;
    const orCondictions = <Record<string, any>[]>[
      { id },
      {
        userId: userData.userId,
        roomName: userData.roomName,
      },
    ];
    if (userData.connectionId) {
      orCondictions.push({ connectionId: userData.connectionId });
    }
    const userFound = await this.prismaService.game_users.findFirst({
      where: {
        OR: orCondictions,
      },
    });
    if (userFound) {
      gameUser = await this.prismaService.game_users.update({
        where: { id: userFound.id },
        data: userData,
      });
    } else {
      gameUser = await this.prismaService.game_users.create({
        data: userData,
      });
    }

    return gameUser;
  }
}
