import { jwtDecode } from 'jwt-decode';
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import * as Sentry from '@sentry/node';
import { Request, Response } from 'express';
import { JwtPayload } from '../types/jwt-payload.type';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    let userId;

    if (request?.headers?.authorization) {
      const decoded = jwtDecode(request.headers.authorization) as JwtPayload;
      userId = decoded?.sub;
    }

    if (
      !(exception instanceof NotFoundException) &&
      !(exception instanceof UnauthorizedException)
    ) {
      Sentry.captureException(exception, {
        extra: {
          url: request.url,
          method: request.method,
          body: request.body,
          query: request.query,
          params: request.params,
          userId,
        },
      });
    }

    const exceptionResponse = exception.getResponse();
    if (exceptionResponse) {
      return response.status(status).json(exceptionResponse);
    }

    response.status(status).json({
      statusCode: status,
      error: exception.message,
      message: exception.message,
    });
  }
}
