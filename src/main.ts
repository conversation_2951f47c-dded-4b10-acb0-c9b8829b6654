import { NestFactory } from '@nestjs/core';
import * as Sentry from '@sentry/node';
import { AppModule } from './app.module';
import { AllExceptionFilter } from './shared/filters/all-exception.filter';
import { PrismaExceptionFilter } from './shared/filters/prisma-exception.filter';
import { VersioningType } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  if (['PRODUCTION', 'STAGING'].includes(process.env.API_MODE || '')) {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
    });

    process.on('uncaughtException', (err) => {
      Sentry.captureException(err);
      console.error('Uncaught Exception:', err);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      Sentry.captureException(reason);
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  app.useGlobalFilters(new AllExceptionFilter());
  app.useGlobalFilters(new PrismaExceptionFilter());

  app.enableCors({
    origin: '*',
  });

  app.enableVersioning({
    type: VersioningType.URI,
  });

  app.enableShutdownHooks();

  await app.listen(process.env.PORT || 3000);
}
bootstrap();
