import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { queuesConst } from 'src/shared/constants/queues';
import { ProcessorsNamesEnumerator } from '../models/enumerators/processors-names.enumerator';

@Injectable()
export class SchedulerService {
  constructor(
    @InjectQueue(queuesConst.scheduler) private readonly scheduleQueue: Queue,
  ) {}

  async scheduleEvent(
    name: ProcessorsNamesEnumerator,
    data: any,
    delay: number,
    jobId?: string,
  ) {
    const jobOptions = {
      delay,
      jobId: jobId || `${Date.now()}`,
      removeOnComplete: true,
      removeOnFail: true,
    };
    await this.scheduleQueue.add(name, data, jobOptions);
  }

  async cancelEventsByJobNameStartsWith(
    startNameString: string,
  ): Promise<void> {
    const jobs = await this.scheduleQueue.getJobs(['delayed', 'waiting']);

    const jobsToRemove = jobs.filter((job) =>
      job.id.startsWith(startNameString),
    );

    for (const job of jobsToRemove) {
      await job.remove();
      console.log(`Job with id ${job.id} cancelled.`);
    }

    if (jobsToRemove.length === 0) {
      console.log(`No jobs with name starting with: ${startNameString}`);
    }
  }
}
