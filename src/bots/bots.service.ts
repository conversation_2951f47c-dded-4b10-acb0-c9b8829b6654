import { Injectable } from '@nestjs/common';
import { botsIdentifiers, botsList } from './botsData';
import { GameUserWithUserEntity } from 'src/game-users/models/entities/game-user.entity';
import { GameWithGameUsersWithUserEntity } from 'src/games/models/entities/game.entity';
import { AnswersModel } from 'src/questions/models/answers.model';
import PontuationModel from 'src/games/models/pontuations.model';

@Injectable()
export class BotsService {
  botPossibleAnswers = [
    'Dunno',
    'Not sure',
    'Fancy that',
    'Perfect!',
    'Interesting 💦',
    'Hot',
    'I like it',
    'Love it',
    '🔥🔥🔥',
    'Not for me',
    "Can't say!",
    'Not too much not too little',
    "I'll keep that private",
    'Any!',
    'Whatever fits the mood 🔥',
    '🤷',
    "That's a good one !",
    'Hmm... 🤔',
    "I'll have to think about it!",
    'You got me there...',
    'That is the question ❗',
    'Let me see your answers first',
    '🥵',
    '💫',
    '🤭',
    '😈',
    '😍',
    '🌈',
    '🧐',
    '😮',
    '💣',
    '🙏',
    '👍',
    '👎',
    '👀',
    'Let me think',
    'Tbh...',
    'Like it like that',
    'Either',
    'Any',
    'None',
    'Depends on the mood',
    'I don’t know',
    'Oh, wow!',
    'Oh yeahhh',
    '👅',
    'Fire away! 🚀',
    'Whatever…',
    'You tell me',
    'I’m blushing 🤭',
    'If I told I’d have to kiss you',
    'wwwuuuhhhhh',
    'Ahahahah Everywhere!',
    'Yes and no',
    'Should I?',
    'Give me a sec…',
    'Brb',
    '🤞',
    '󰵗󰵗',
    'Uuhh I’m blushing!',
    'Feeling hot 💦',
    'Somebody turn the AC on!',
    '🙊🙊',
    'Ooopss',
    'I’ll let you know later 😏',
    'Let’s get it on 🎶',
    '🫶🫶🫶 (Heart hands)',
    'This is making me hungry!',
    '😏😏😏',
    'Sí, yes, da!',
    'I’ll think about that',
    'Just here for the quyckies',
    'Give me some quyckies',
    'Never thought about that…',
    'Good idea! 😁',
    'Yes, no… maybe',
    'All of it',
    'None of the above',
    'This is fun!',
    'I’ll let you decide I guess….',
    'Okay!',
    'Whatevzzz',
    'Yup!',
    'There we go!🔥',
    '🚿🚿 💦💦',
    'I’m turning the lights off 🙈🙈',
    'Cheers!',
    'Come again?',
    'I love your answers !',
    'This is sooo cool!',
    'Oh, wow',
    'IDK',
    'Knowing me, knowing you',
    'That`s hot',
  ];

  constructor() {}
  public selectBots(
    roomName: string,
    quizId: number,
    exclude: string[],
  ): GameUserWithUserEntity {
    const filteredItems = botsIdentifiers.filter(
      (item) => !exclude.includes(item),
    );

    if (filteredItems.length === 0) {
      return null;
    }

    const randomIndex = Math.floor(Math.random() * filteredItems.length);

    const res = <GameUserWithUserEntity>botsList[filteredItems[randomIndex]];
    res.roomName = roomName;
    res.quizId = quizId;
    res.userId = res.user.id;

    return res;
  }

  getBotsPontuations(
    gameData: GameWithGameUsersWithUserEntity,
  ): PontuationModel[] {
    const pontuations = [];
    const currentQuestion = gameData.data.questions[gameData.round - 1];

    gameData.game_users.forEach((gameUser) => {
      const isBot = botsIdentifiers.includes(gameUser.user.identifier);
      if (isBot) {
        let targetUser = null;
        do {
          targetUser =
            gameData.game_users[
              Math.floor(Math.random() * gameData.game_users.length)
            ];
        } while (targetUser.user.identifier == gameUser.user.identifier);
        const newPoints = {
          currentRound: gameData.round,
          userThatSetPontuationIdentifier: gameUser.user.identifier,
          userThatReceivePontuationIdentifier: targetUser.user.identifier,
          pontuation: Math.random() > 0.5 ? 10 : 0,
          questionId: currentQuestion.id,
        } as PontuationModel;
        pontuations.push(newPoints);
      }
    });
    return pontuations;
  }
  getBotsAnswers(gameData: GameWithGameUsersWithUserEntity): AnswersModel[] {
    const answers = [];
    const currentQuestion = gameData.data.questions[gameData.round - 1];

    gameData.game_users.forEach((gameUser) => {
      const isBot = botsIdentifiers.includes(gameUser.user.identifier);
      if (isBot) {
        let answer = '';
        if (currentQuestion.type == 'choice') {
          const optionIndex = Math.floor(
            Math.random() * currentQuestion.questions_options.length,
          );
          answer = currentQuestion.questions_options[optionIndex].value;
        } else {
          const optionIndex = Math.floor(
            Math.random() * this.botPossibleAnswers.length,
          );
          answer = this.botPossibleAnswers[optionIndex];
        }

        answers.push(<AnswersModel>{
          currentRound: gameData.round,
          questionId: currentQuestion.id,
          userIndentifier: gameUser.user.identifier,
          answer,
          user_id: Number(gameUser.user.id),
          is_correct_answer: false,
          questions_options_id: '',
        });
      }
    });
    return answers;
  }
}
