import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { Reflector } from '@nestjs/core';
import { JwtPayload } from '../types/jwt-payload.type';
import { verify } from 'jsonwebtoken';

@Injectable()
export class UserTokenAuth extends JwtAuthGuard implements CanActivate {
  constructor(protected readonly reflector: Reflector) {
    super(reflector);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const token = this.extractToken(context);
    if (!token) {
      return await super.canActivate(context);
    }

    // const user = await this.findUserByApiKey(token);
    // if (!user) {
    //   return await super.canActivate(context);
    // }

    const decoded = verify(token, process.env.JWT_SECRET) as JwtPayload;

    const request = context.switchToHttp().getRequest();

    // const jwtPayload: JwtPayload = {
    //   user: user,
    //   sub: user.id,
    //   exp: 0,
    //   iat: 0,
    // };
    request.user = decoded.user;

    return true;
  }

  private extractToken(context: ExecutionContext): string {
    const request = context.switchToHttp().getRequest();

    const token =
      request.headers['x-token'] ||
      request.query['xToken'] ||
      request.body['xToken'];

    return token;
  }

  // private async findUserByApiKey(key: string) {
  //   const apiKey = await this.apiKeysService.findApiKey({ key: key });

  //   if (!apiKey) {
  //     return null;
  //   }

  //   const user = await this.usersService.findUser({
  //     companyId: apiKey.companyId,
  //   });

  //   return user;
  // }
}
