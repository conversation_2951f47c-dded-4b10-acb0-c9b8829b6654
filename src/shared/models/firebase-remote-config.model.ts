export interface ParameterGroups {}

export interface Parameters {
  GAME_ROUNDS: Game;
  GAME_TIME_ROUND_ANSWER: Game;
  GAME_TIME_ROUND_QUESTION: Game;
  GAME_QUESTIONS_SEQUENCE: Game;
  DEV_GAME_QUESTIONS_SEQUENCE: Game;
  GAME_TIME_ROUND_QUESTION_DIRECT: Game;
  GAME_TIME_ROUND_QUESTION_MULTIPLE: Game;
  IS_PUBLICATION_MODE: Game;
  GAME_TIME_WAITING_START_GAME_MULTIPLAYER: Game;
  GAME_TIME_WAITING_START_GAME_SINGLEPLAYER: Game;
  GAME_TIME_ROUND_QUESTION_SINGLEPLAYER: Game;
  GAME_TIME_ROUND_ANSWER_SINGLEPLAYER: Game;
}

export interface Game {
  defaultValue: DefaultValue;
  description: string;
  valueType: string;
}

export interface DefaultValue {
  value: string;
}

export interface Version {
  versionNumber: string;
  updateOrigin: string;
  updateType: string;
  updateUser: UpdateUser;
  updateTime: string;
}

export interface UpdateUser {
  email: string;
}

interface FirebaseRemoteConfigModel {
  conditions: any[];
  parameters: Parameters;
  parameterGroups: ParameterGroups;
  etag: string;
  version: Version;
}

export default FirebaseRemoteConfigModel;
