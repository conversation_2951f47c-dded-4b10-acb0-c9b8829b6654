enum ChatActionEnumerator {
  // inbound
  sendMessage = 'sendMessage',
  getMessages = 'getMessages',
  getConversations = 'getConversations',
  markAsRead = 'markAsRead',
  getUnreadCount = 'getUnreadCount',
  getMessagesBetweenUsers = 'getMessagesBetweenUsers',
  
  // outbound
  messageReceived = 'messageReceived',
  messagesUpdated = 'messagesUpdated',
  conversationsUpdated = 'conversationsUpdated',
  unreadCountUpdated = 'unreadCountUpdated',
}

export default ChatActionEnumerator;
