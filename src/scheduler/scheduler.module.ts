import { forwardRef, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { SchedulerService } from './services/scheduler.service';
import { SchedulerProcessor } from './processors/scheduler.processor';
import { queuesConst } from 'src/shared/constants/queues';
import { GamesModule } from 'src/games/games.module';

@Module({
  imports: [
    BullModule.registerQueue({
      name: queuesConst.scheduler,
    }),
    forwardRef(() => GamesModule),
  ],
  providers: [SchedulerService, SchedulerProcessor],
  exports: [SchedulerService],
})
export class SchedulerModule {}
