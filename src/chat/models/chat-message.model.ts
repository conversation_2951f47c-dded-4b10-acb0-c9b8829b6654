export interface ChatMessageModel {
  id: number;
  senderIdentifier: string;
  receiverIdentifier: string;
  content: string;
  created_at: Date;
  is_read: boolean;
  sender?: {
    id: number;
    identifier: string;
    name: string;
    avatar?: string;
  };
  receiver?: {
    id: number;
    identifier: string;
    name: string;
    avatar?: string;
  };
}

export interface ChatConversationModel {
  userIdentifier: string;
  userName: string;
  userAvatar?: string;
  lastMessage: ChatMessageModel;
  unreadCount: number;
}

export interface ChatMessageResponse {
  messages: ChatMessageModel[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export default ChatMessageModel;
