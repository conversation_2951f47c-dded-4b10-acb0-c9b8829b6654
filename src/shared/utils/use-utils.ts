import DefaultObjectInterface from '../types/default-object.interface';

function useUtils() {
  function getHeaders({
    headers,
    unauthorized,
  }: {
    headers?: DefaultObjectInterface;
    unauthorized?: boolean;
  }): DefaultObjectInterface {
    const res = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'Accept-Encoding': 'application/json',
    };

    if (!unauthorized && process.env.EXTERNAL_API_TOKEN != '') {
      res['Authorization'] = `Bearer ${process.env.EXTERNAL_API_TOKEN}`;
    }

    if (headers != null) {
      Object.keys(headers).forEach((k) => {
        if (unauthorized && k.toLowerCase() === 'authorization') return;
        res[k] = headers[k];
      });
    }

    return res;
  }

  return { getHeaders };
}

export default useUtils;
