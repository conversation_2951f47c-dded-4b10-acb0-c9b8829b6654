import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateCommand } from '../create.command';
import GameUserEntity from 'src/game-users/models/entities/game-user.entity';
import { PrismaService } from 'src/prisma/prisma.service';

@CommandHandler(CreateCommand)
export class CreateHandler implements ICommandHandler<CreateCommand> {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(command: CreateCommand): Promise<GameUserEntity> {
    return this.prismaService.game_users.create({ data: command.data });
  }
}
