import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { Chat<PERSON>ontroller } from './chat.controller';
import { ChatService } from './chat.service';
import { PushNotificationService } from './services/push-notification.service';
import { PrismaModule } from '../prisma/prisma.module';
import { SendChatMessageHandler } from './commands/handlers/send-chat-message.handler';

const CommandHandlers = [SendChatMessageHandler];

@Module({
  imports: [PrismaModule, CqrsModule],
  controllers: [ChatController],
  providers: [ChatService, PushNotificationService, ...CommandHandlers],
  exports: [ChatService, PushNotificationService],
})
export class ChatModule {}
