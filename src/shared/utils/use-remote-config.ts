import FirebaseRemoteConfigModel from '../models/firebase-remote-config.model';
import GameConfigParamsModel from '../models/game-config-params.model';
import admin from 'firebase-admin';
import DefaultObjectInterface from '../types/default-object.interface';

export default function useRemoteConfig() {
  const defaultFirebaseData: GameConfigParamsModel = {
    maxRoundsInGame: 4,
    timeAnsweringRound: 12,
    timeReviewingRound: 4,
    timeWaitingStartingGame: 4,
    timeWaitingStartGameMultiplayer: 4,
    timeWaitingStartGameSingleplayer: 4,
    gameRounds: 4,
    gameQuestionsSequence: 'IMIM',
    devGameQuestionsSequence: 'IMIM',
    timeAnsweringQuestionDirect: 8,
    timeAnsweringQuestionMultiple: 12,
    isPublicationMode: false,
    timeAnsweringRoundSingleplayer: 12,
    timeReviewingRoundSingleplayer: 5,
  };
  let _firebaseConf: DefaultObjectInterface | null = null;

  function defineFirebaseConf(): boolean {
    if (!process.env.FIREBASE_CONF) {
      return false;
    }
    if (_firebaseConf) {
      return true;
    }
    const parsedStr = process.env.FIREBASE_CONF.replace(/\n/g, '\n');
    _firebaseConf = JSON.parse(parsedStr);
    admin.initializeApp({
      credential: admin.credential.cert(_firebaseConf as admin.ServiceAccount),
    });
    return true;
  }

  async function getRemoteConfigTemplate(
    defaultData: GameConfigParamsModel,
  ): Promise<GameConfigParamsModel> {
    defineFirebaseConf();

    const config = admin.remoteConfig();
    const result = {
      maxRoundsInGame: defaultData.maxRoundsInGame,
      timeAnsweringRound: defaultData.timeAnsweringRound,
      timeReviewingRound: defaultData.timeReviewingRound,
      timeWaitingStartingGame: defaultData.timeWaitingStartingGame,
      timeWaitingStartGameMultiplayer:
        defaultData.timeWaitingStartGameMultiplayer,
      timeWaitingStartGameSingleplayer:
        defaultData.timeWaitingStartGameSingleplayer,
      gameRounds: defaultData.gameRounds,
      gameQuestionsSequence: defaultData.gameQuestionsSequence,
      devGameQuestionsSequence: defaultData.devGameQuestionsSequence,
      timeAnsweringQuestionDirect: defaultData.timeAnsweringQuestionDirect,
      timeAnsweringQuestionMultiple: defaultData.timeAnsweringQuestionMultiple,
      isPublicationMode: defaultData.isPublicationMode,
      timeAnsweringRoundSingleplayer:
        defaultData.timeAnsweringRoundSingleplayer,
      timeReviewingRoundSingleplayer:
        defaultData.timeReviewingRoundSingleplayer,
    };
    return new Promise(async (resolve, reject) => {
      try {
        const res = <FirebaseRemoteConfigModel>(
          (<unknown>await config.getTemplate())
        );
        result.maxRoundsInGame = Number(
          res.parameters.GAME_ROUNDS.defaultValue.value,
        );
        result.timeAnsweringRound = Number(
          res.parameters.GAME_TIME_ROUND_QUESTION.defaultValue.value,
        );
        result.timeAnsweringQuestionDirect = Number(
          res.parameters.GAME_TIME_ROUND_QUESTION_DIRECT.defaultValue.value,
        );
        result.timeAnsweringQuestionMultiple = Number(
          res.parameters.GAME_TIME_ROUND_QUESTION_MULTIPLE.defaultValue.value,
        );
        result.timeWaitingStartGameMultiplayer = Number(
          res.parameters.GAME_TIME_WAITING_START_GAME_MULTIPLAYER.defaultValue
            .value,
        );
        result.devGameQuestionsSequence = String(
          res.parameters.DEV_GAME_QUESTIONS_SEQUENCE.defaultValue.value,
        );
        result.timeWaitingStartGameSingleplayer = Number(
          res.parameters.GAME_TIME_WAITING_START_GAME_SINGLEPLAYER.defaultValue
            .value,
        );
        result.gameRounds = Number(
          res.parameters.GAME_ROUNDS.defaultValue.value,
        );
        result.gameQuestionsSequence = String(
          res.parameters.GAME_QUESTIONS_SEQUENCE.defaultValue.value,
        );
        result.timeReviewingRound = Number(
          res.parameters.GAME_TIME_ROUND_ANSWER.defaultValue.value,
        );
        result.isPublicationMode =
          res.parameters.IS_PUBLICATION_MODE.defaultValue.value == 'true';
        this.currentRemoteConfigData = result;

        result.timeAnsweringRoundSingleplayer = Number(
          res.parameters.GAME_TIME_ROUND_QUESTION_SINGLEPLAYER.defaultValue
            .value,
        );

        result.timeReviewingRoundSingleplayer = Number(
          res.parameters.GAME_TIME_ROUND_ANSWER_SINGLEPLAYER.defaultValue.value,
        );
        this.lastRemoteConfigChange = Date.now();
      } catch (e) {
        if (process.env.VERBOSE == 'true') {
          console.log('==>');
        }
        reject(defaultData);
      }

      resolve(result);
    });
  }

  return { defaultFirebaseData, getRemoteConfigTemplate };
}
