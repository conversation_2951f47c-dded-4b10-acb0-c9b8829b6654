import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { SchedulerEventCommand } from '../scheduler-event.command';
import { SchedulerService } from 'src/scheduler/services/scheduler.service';
import { ProcessorsNamesEnumerator } from 'src/scheduler/models/enumerators/processors-names.enumerator';

@CommandHandler(SchedulerEventCommand)
export class SchedulerEventHandler
  implements ICommandHandler<SchedulerEventCommand>
{
  constructor(private readonly schedulerService: SchedulerService) {}

  async execute(command: SchedulerEventCommand): Promise<boolean> {
    try {
      await this.schedulerService.scheduleEvent(
        ProcessorsNamesEnumerator.gameEvents,
        command.event,
        command.timeInSeconds * 1000,
        `${command.roomName}-${command.event.action}-${Date.now()}`,
      );
      return true;
    } catch (error) {
      console.error('Failed to start redis counter execution:', error);
      return false;
    }
  }
}
