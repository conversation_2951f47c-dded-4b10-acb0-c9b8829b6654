import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { SQS, SendMessageCommandInput } from '@aws-sdk/client-sqs';
import { SendDataToQueueCommand } from '../send-data-to-queue.command';

@CommandHandler(SendDataToQueueCommand)
export class SendDataToQueueHandler
  implements ICommandHandler<SendDataToQueueCommand>
{
  sqs: SQS;
  async execute(command: SendDataToQueueCommand): Promise<void> {
    this.sendData(command.data);
  }

  constructor() {
    this.sqs = new SQS({
      region: process.env.AWS_REGION,
    });
  }

  async sendData(payload: unknown): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      const params = <SendMessageCommandInput>{
        MessageBody: JSON.stringify(payload),
        QueueUrl: process.env.AWS_SQS_URL,
      };

      try {
        await this.sqs.sendMessage(params);
        resolve(true);
      } catch (err) {
        reject(false);
      }
    });
  }
}
