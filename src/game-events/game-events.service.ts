import { Injectable } from '@nestjs/common';
import GameEventModel from './model/game-event.model';
import { CommandBus } from '@nestjs/cqrs';
import { UpsertEventCommand } from './commands/upsert-event.command';
import { PrismaService } from 'src/prisma/prisma.service';
import GameEventEntity from './model/entities/game-event.entity';
import { CancelEventCommand } from './commands/cancel-event.command';

@Injectable()
export class GameEventsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly commandBus: CommandBus,
  ) {}

  public async upsertGameEvent(gameEvent: GameEventModel) {
    return await this.commandBus.execute(new UpsertEventCommand(gameEvent));
  }

  public async getGameEventByEventId(
    eventId: string,
  ): Promise<GameEventEntity> {
    return await this.prismaService.game_events.findFirst({
      where: {
        event_id: eventId,
      },
    });
  }

  public async getGameEventsByRoomNameAndStatus(
    roomName: string,
    isExecuted?: boolean,
  ) {
    return await this.prismaService.game_events.findMany({
      where: {
        room_name: roomName,
        is_executed: isExecuted,
      },
    });
  }

  public async cancelEvent(eventId: string) {
    await this.upsertGameEvent({
      eventId: eventId,
      isExecuted: true,
      roomName: '',
    });
    return await this.commandBus.execute(new CancelEventCommand(eventId));
  }

  public async cancelEvents(eventIds: string[]) {
    for (let i = 0; i < eventIds.length; i++) {
      await this.cancelEvent(eventIds[i]);
    }
  }
}
