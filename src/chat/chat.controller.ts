import { Head<PERSON>, Body, Controller, Post } from '@nestjs/common';
import { ChatService } from './chat.service';
import ChatMessageDTO, {
  ChatDataDTO,
  createChatDataDTO,
} from './models/dtos/chat-message.dto';
import SendMessageDTO from './models/dtos/send-message.dto';
import GetMessagesDTO, {
  GetMessagesBetweenUsersDTO,
} from './models/dtos/get-messages.dto';
import MarkAsReadDTO from './models/dtos/mark-as-read.dto';

@Controller('chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  getData(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<any>,
  ): ChatDataDTO<any> {
    const { authorization } = headers;
    const connectionId = headers['connectionid'] || headers['connectionId'];

    return createChatDataDTO({
      connectionId,
      token: authorization || body.authorization,
      data: body.data,
    });
  }

  @Post('/')
  async default(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<any>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }

  @Post('/send')
  async sendMessage(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<SendMessageDTO>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }

  @Post('/messages')
  async getMessages(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<GetMessagesDTO>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }

  @Post('/conversations')
  async getConversations(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<any>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }

  @Post('/mark-read')
  async markAsRead(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<MarkAsReadDTO>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }

  @Post('/unread-count')
  async getUnreadCount(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<any>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }

  @Post('/messages-between-users')
  async getMessagesBetweenUsers(
    @Headers() headers: Record<string, string>,
    @Body() body: ChatMessageDTO<GetMessagesBetweenUsersDTO>,
  ) {
    const params = this.getData(headers, body);
    return await this.chatService.processAction(params);
  }
}
