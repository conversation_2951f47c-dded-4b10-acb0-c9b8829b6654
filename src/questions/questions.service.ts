import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { QuestionsModel } from 'src/questions/models/questions.model';
import useUtils from 'src/shared/utils/use-utils';
import * as https from 'https';
import showDebugConsole from 'src/shared/utils/show-debug-console';

@Injectable()
export class QuestionsService {
  utils = useUtils();

  async getQuestions({
    questionsSequence,
    userId,
  }: {
    questionsSequence: string;
    userId: string;
  }): Promise<QuestionsModel[]> {
    try {
      const agent = new https.Agent({
        rejectUnauthorized: false, // Desabilita a verificação do certificado SSL
      });

      showDebugConsole(
        `trying to get questions using url: ` +
          `${process.env.EXTERNAL_API_URL}/questions/generateWithSequence?sequence_questions=${questionsSequence}`, //user_id=${userId}
      );

      const response = await axios.get<QuestionsModel[]>(
        `${process.env.EXTERNAL_API_URL}/questions/generateWithSequence?sequence_questions=${questionsSequence}`, //user_id=${userId}
        {
          httpsAgent: agent,
          headers: this.utils.getHeaders({}),
        },
      );
      if (response.status === 200 && response.data) {
        return response.data;
      } else {
        return [];
      }
    } catch (error) {
      showDebugConsole(`error trying to get questions: ` + error);
      return [];
    }
  }
}
