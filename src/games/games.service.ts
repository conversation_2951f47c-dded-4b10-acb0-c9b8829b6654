import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import GameDataDTO from './models/dtos/game-data.dto';
import PlayerEnteringGameDTO from './models/dtos/player-entering-game.dto';
import GameModeEnumerator from './models/enumerators/game-mode.enumerator';
import { GameUsersService } from 'src/game-users/game-users.service';
import { CommandBus } from '@nestjs/cqrs';
import { SendMessageToGameUsersCommand } from './commands/send-message-to-game-users.command';
import GameEntity, {
  gamesToGamesEntity,
  GameWithGameUsersWithUserEntity,
} from './models/entities/game.entity';
import GameMessageDTO from './models/dtos/game-message.dto';
import GameActionEnumerator from './models/enumerators/game-action.enumerator';
import {
  mapRoomModelToRoomEntity,
  RoomsModel,
} from '../rooms/models/rooms.model';
import GameUserEntity, {
  GameUserWithUserEntity,
} from 'src/game-users/models/entities/game-user.entity';
import GameConfigParamsModel from 'src/shared/models/game-config-params.model';
import useRemoteConfig from 'src/shared/utils/use-remote-config';
import UseRemoteConfigType from 'src/shared/types/use-remote-config.type';
import { CreateNewGameRoomCommand } from './commands/create-new-game-room.command';
import { QuestionsService } from 'src/questions/questions.service';
import GameFailureDTO from './models/dtos/game-failure.dto';
import GameErrorEnumerator from './models/enumerators/game-error.enumerator';
import { UpdateGameCommand } from './commands/update-game.command';
import GameStateEnumerator from './models/enumerators/game-state.enumerator';
import ChangeGameStateDTO from './models/dtos/change-game-state.dto';
import {
  AnswersModel,
  UserAnswerModel,
} from 'src/questions/models/answers.model';
import { SendDataToQueueCommand } from './commands/send-data-to-queue.command';
import { CreateEventCommand } from './commands/create-event.command';
import { GameEventsService } from 'src/game-events/game-events.service';
import { BotsService } from 'src/bots/bots.service';
import AddBotDTO from './models/dtos/add-bot.dto';
import { UsersModel } from './models/users.model';
import StartGameDTO from './models/dtos/start-game.dto';
import { RoomsService } from 'src/rooms/rooms.service';
import { RoomsEntity } from './models/entities/rooms.entity';
import { EndGameDataModel } from 'src/rooms/models/end-game-data.model';
import { SchedulerEventCommand } from './commands/scheduler-event.command';
import { SchedulerService } from 'src/scheduler/services/scheduler.service';
import showDebugConsole from 'src/shared/utils/show-debug-console';
import PlayerGivingGamePointDTO from './models/dtos/player-giving-game-point.dto';
import { GamePointTypeEnumerator } from './models/enumerators/game-point-type.enumerator';
import PontuationModel from './models/pontuations.model';

@Injectable()
export class GamesService {
  firebaseRemoteConfigData: GameConfigParamsModel = null;
  firebaseRemoteConfig: UseRemoteConfigType = undefined;
  lastRemoteConfigUpdate: Date = new Date();

  constructor(
    private readonly prismaService: PrismaService,
    private readonly commandBus: CommandBus,
    private readonly questionsService: QuestionsService,
    private readonly gameUsersService: GameUsersService,
    private readonly gameEventsService: GameEventsService,
    private readonly botsService: BotsService,
    private readonly roomsService: RoomsService,
    private readonly schedulerService: SchedulerService,
  ) {
    this.firebaseRemoteConfig = useRemoteConfig();
    this.firebaseRemoteConfigData =
      this.firebaseRemoteConfig.defaultFirebaseData;
    this.getRemoteConfigData({ skipVerification: true });
  }

  private async getRemoteConfigData({
    skipVerification,
  }: {
    skipVerification?: boolean;
  }) {
    if (!this.lastRemoteConfigUpdate) {
      return false;
    }
    if (
      skipVerification ||
      Date.now() - this.lastRemoteConfigUpdate.getTime() > 3000
    ) {
      this.firebaseRemoteConfigData =
        await this.firebaseRemoteConfig.getRemoteConfigTemplate(
          this.firebaseRemoteConfigData,
        );
      this.lastRemoteConfigUpdate = new Date();
    }
  }

  private async sendUserMessage(params: GameMessageDTO<any>) {
    return await this.commandBus.execute(
      new SendMessageToGameUsersCommand(params),
    );
  }

  async getGameByRoomName(
    name: string,
    includeGameUsers = true,
  ): Promise<
    GameWithGameUsersWithUserEntity | GameWithGameUsersWithUserEntity | null
  > {
    const res = await this.prismaService.games.findFirst({
      where: { name },
      include: {
        game_users: {
          include: {
            user: includeGameUsers,
          },
        },
      },
    });
    return gamesToGamesEntity<GameWithGameUsersWithUserEntity>(res);
  }

  private async addOrUpdateUser(params: {
    isRoomOwner: boolean;
    gameUserId?: number;
    data: GameDataDTO<PlayerEnteringGameDTO>;
  }): Promise<GameUserEntity> {
    return await this.gameUsersService.upsertGameUser(<GameUserEntity>{
      id: params.gameUserId,
      connectionId: params.data.connectionId,
      roomName: params.data.data.roomName,
      roomOwner: params.isRoomOwner,
      userId: BigInt(params.data.data.userData.id),
      quizId: params.data.data.quizId,
      isConnected: true,
      isBot: params.data.data.isBot,
    });
  }

  async createNewGame(
    gameMode: GameModeEnumerator,
    quizId: number,
    userId: string,
    roomName?: string,
    spotName?: string,
  ) {
    showDebugConsole('creating a new game: ' + quizId);
    await this.getRemoteConfigData({});

    const questions = await this.questionsService.getQuestions({
      questionsSequence:
        process.env.API_MODE === 'DEV'
          ? this.firebaseRemoteConfigData.devGameQuestionsSequence
          : this.firebaseRemoteConfigData.gameQuestionsSequence,
      userId,
    });
    // if (questions.length == 0) return '';

    const res = await this.commandBus.execute(
      new CreateNewGameRoomCommand(
        gameMode,
        quizId,
        this.firebaseRemoteConfigData,
        questions,
        roomName,
        spotName,
      ),
    );
    return res;
  }

  async sendFailMessage(
    connectionId: string,
    data: GameFailureDTO,
  ): Promise<GameMessageDTO<GameFailureDTO>> {
    const message = {
      action: GameActionEnumerator.failure,
      connectionId,
      authorization: '',
      data: data,
    } as GameMessageDTO<GameFailureDTO>;
    return await this.sendUserMessage(message);
  }

  private async updateGame(gameData: GameEntity) {
    const res = await this.commandBus.execute(new UpdateGameCommand(gameData));

    return res;
  }

  private async getOpenedGame(
    quizId: number,
  ): Promise<GameWithGameUsersWithUserEntity | null> {
    showDebugConsole('trying to get opened room for quiz: ' + quizId);
    const games = await this.prismaService.games.findMany({
      where: {
        // quiz_id: quizId,
        // game_mode: gameMode,
        game_mode: GameModeEnumerator.fourRandom,
        state: GameStateEnumerator.waitingPlayers,
        NOT: {
          name: {
            contains: 'private',
          },
        },
      },
      include: {
        game_users: {
          include: {
            user: true,
          },
        },
      },
    });

    showDebugConsole('found games: ' + games.length);

    for (let i = 0; i < games.length; i++) {
      if (games[i].game_users.length < games[i].max_users) {
        showDebugConsole('found game room opened');
        return gamesToGamesEntity<GameWithGameUsersWithUserEntity>(games[i]);
      }
    }
    return null;
  }

  private async getOpenedSpotMatchGame(
    spotName: string,
  ): Promise<GameWithGameUsersWithUserEntity | null> {
    showDebugConsole(
      'trying to get opened spotMatch room for spot: ' + spotName,
    );
    const games = await this.prismaService.games.findMany({
      where: {
        game_mode: 'spotMatch' as any,
        state: GameStateEnumerator.waitingPlayers,
        name: {
          startsWith: `${spotName}--`,
        },
      },
      include: {
        game_users: {
          include: {
            user: true,
          },
        },
      },
    });

    showDebugConsole('found spotMatch games: ' + games.length);

    for (let i = 0; i < games.length; i++) {
      if (games[i].game_users.length < games[i].max_users) {
        showDebugConsole('found spotMatch game room opened');
        return gamesToGamesEntity<GameWithGameUsersWithUserEntity>(games[i]);
      }
    }
    return null;
  }

  async newUserEntering(userGameInfo: GameDataDTO<PlayerEnteringGameDTO>) {
    const gameUserParams = {
      isRoomOwner: true,
      gameUserId: Number(userGameInfo.data.userData.id),
      data: userGameInfo,
    };

    let openedGame: GameWithGameUsersWithUserEntity | null = null;

    // Handle different game modes
    if (userGameInfo.data.gameMode === GameModeEnumerator.oneOnInvitedPlayers) {
      openedGame = null; // Always create new room for invited players
    } else if (userGameInfo.data.gameMode === GameModeEnumerator.spotMatch) {
      // For spotMatch, look for existing rooms with the same spotName
      if (userGameInfo.data.spotName) {
        openedGame = await this.getOpenedSpotMatchGame(
          userGameInfo.data.spotName,
        );
      }
    } else {
      // For other modes (fourRandom, singlePlayer), use existing logic
      openedGame = await this.getOpenedGame(userGameInfo.data.quizId);
    }

    if (openedGame) {
      gameUserParams.isRoomOwner = false;
      gameUserParams.data.data.roomName = openedGame.name;
    } else {
      showDebugConsole('starting to create a new room');
      const roomName = await this.createNewGame(
        userGameInfo.data.gameMode,
        userGameInfo.data.quizId,
        String(userGameInfo.data.userData.id),
        undefined, // roomName
        userGameInfo.data.spotName, // spotName for spotMatch mode
      );
      showDebugConsole('room created name: ' + roomName);
      if (roomName == '') {
        return await this.sendFailMessage(userGameInfo.connectionId, {
          code: GameErrorEnumerator.withoutQuestions,
          message: '',
        } as GameFailureDTO);
      }
      gameUserParams.data.data.roomName = roomName;
      gameUserParams.isRoomOwner = true;
      openedGame = await this.getGameByRoomName(roomName);
      if (openedGame.game_mode == GameModeEnumerator.fourRandom) {
        await this.createCancelGameEvent({
          roomName,
          timeInSeconds: 45,
        });
        await this.createAddBotEvent({ roomName, quizId: openedGame.quiz_id });
      }
    }
    await this.addOrUpdateUser(gameUserParams);
    const newUser = await this.gameUsersService.getUserByConnectionId(
      userGameInfo.connectionId,
    );
    openedGame.game_users.push(newUser);

    await this.sendWelcome(userGameInfo.connectionId, openedGame);
    await this.sendUserEntered(openedGame.game_users);

    if (
      openedGame.game_users.length === openedGame.max_users ||
      openedGame.game_mode === GameModeEnumerator.singlePlayer
    ) {
      // await this.autoCreateGameEvents(userGameInfo.token, openedGame.name);
      await this.sendGameStarting(
        openedGame.name,
        openedGame.game_users,
        openedGame.data.timeWaitingStartingGame,
      );
    }
  }

  private async createGameEvent(
    timeInSeconds: number,
    roomName: string,
    event: GameMessageDTO<any>,
  ) {
    await this.commandBus.execute(
      new (process.env.USE_SCHEDULER && process.env.USE_SCHEDULER == 'true'
        ? SchedulerEventCommand
        : CreateEventCommand)(timeInSeconds, roomName, event),
    );
  }

  private createEvent(
    timeWhenExec: Date,
    detailType: string,
    roomName: string,
    detail: string,
  ) {
    return {
      Time: timeWhenExec.toISOString(),
      Source: 'game.events',
      DetailType: detailType,
      Detail: detail,
      EventBusName: 'default',
    };
  }

  private getEventDetail<T>(
    action: GameActionEnumerator,
    data: T,
    connectionId = '',
  ): string {
    const resp = <GameMessageDTO<ChangeGameStateDTO>>{
      action: action,
      authorization: '',
      connectionId: connectionId,
      data,
    };
    return JSON.stringify(resp);
  }

  // private async autoCreateGameEvents(token: string, roomName: string) {
  // const gameData = await this.getGameByRoomName(roomName);
  // const events = <EventModel[]>[];
  // const qtdRounds = gameData.data.questions.length;
  // let lastTime =
  //   Math.floor(Date.now() / 1000) + gameData.data.timeWaitingStartingGame;
  // for (let i = 1; i <= qtdRounds; i++) {
  //   events.push(
  //     this.createEvent(
  //       lastTime,
  //       GameStateEnumerator.answering,
  //       roomName,
  //       this.getEventDetail<ChangeGameStateDTO>(
  //         GameActionEnumerator.changeState,
  //         <ChangeGameStateDTO>{
  //           roomName: roomName,
  //           round: i,
  //           state: GameStateEnumerator.answering,
  //         },
  //       ),
  //     ),
  //   );
  //   lastTime = lastTime + gameData.data.timeAnsweringRound;
  //   events.push(
  //     this.createEvent(
  //       lastTime,
  //       GameStateEnumerator.reviewing,
  //       roomName,
  //       this.getEventDetail<ChangeGameStateDTO>(
  //         GameActionEnumerator.changeState,
  //         <ChangeGameStateDTO>{
  //           roomName: roomName,
  //           round: i,
  //           state: GameStateEnumerator.reviewing,
  //         },
  //       ),
  //     ),
  //   );

  //   lastTime = lastTime + gameData.data.timeReviewingRound;
  //   if (i == qtdRounds) {
  //     events.push(
  //       this.createEvent(
  //         lastTime,
  //         GameStateEnumerator.endGameReview,
  //         roomName,
  //         this.getEventDetail<ChangeGameStateDTO>(
  //           GameActionEnumerator.changeState,
  //           <ChangeGameStateDTO>{
  //             roomName: roomName,
  //             round: i,
  //             state: GameStateEnumerator.endGameReview,
  //           },
  //         ),
  //       ),
  //     );
  //   }
  // }

  // await this.commandBus.execute(new CreateEventsCommand(events));
  // }

  private getProcessedGameUsers(
    gameUsers: GameUserWithUserEntity[],
  ): GameUserWithUserEntity[] {
    const url = process.env.BUCKET_AVATAR_URL;
    const res = gameUsers.map((gameSocketUser) => ({
      ...gameSocketUser,
      user: {
        ...gameSocketUser.user,
        avatarUrl: `${url}/${gameSocketUser.user.avatar}`,
      },
    }));
    return res;
  }

  private async sendBotsAnswers(roomName: string) {
    const gameData = await this.getGameByRoomName(roomName);
    if (
      !gameData ||
      [GameStateEnumerator.closed, GameStateEnumerator.cancelled].includes(
        gameData.state as GameStateEnumerator,
      )
    ) {
      return;
    }
    const botsAnswers = this.botsService.getBotsAnswers(gameData);
    gameData.data.answers.push(...botsAnswers);

    await this.updateGame(gameData);
    const completeGameData = await this.getGameByRoomName(gameData.name);

    return await this.updateQuestions(
      completeGameData.game_users,
      completeGameData.data.answers,
    );
  }

  private async sendBotsPontuations(roomName: string) {
    const gameData = await this.getGameByRoomName(roomName);
    if (
      !gameData ||
      [GameStateEnumerator.closed, GameStateEnumerator.cancelled].includes(
        gameData.state as GameStateEnumerator,
      )
    ) {
      return;
    }
    const botsPontuations = this.botsService.getBotsPontuations(gameData);
    gameData.data.pontuations.push(...botsPontuations);

    await this.updateGame(gameData);
    const completeGameData = await this.getGameByRoomName(gameData.name);

    botsPontuations.forEach((pontuation) => {
      this.playerReceivePontuation(completeGameData.game_users, pontuation);
    });

    // return await this.updatePontuations(
    //   completeGameData.game_users,
    //   completeGameData.data.pontuations,
    // );
  }

  async setGameState(
    roomName: string,
    newState: GameStateEnumerator,
    round?: number,
  ): Promise<GameEntity | boolean> {
    const gameData = await this.getGameByRoomName(roomName, false);

    if (round != undefined && round < gameData.round) {
      return false;
    }

    gameData.state = newState;
    gameData.data.gameState = newState;
    gameData.round = round || gameData.round;

    return await this.updateGame(gameData);
  }

  async sendWelcome(
    connectionId: string,
    gameData: GameWithGameUsersWithUserEntity,
  ) {
    showDebugConsole(`Sending welcome message - ${gameData.game_users.length}`);
    const gameUsers = this.getProcessedGameUsers(gameData.game_users);
    showDebugConsole(`Sending welcome message - 2: ${gameUsers.length}`);
    const room = {
      ...gameData.data,
      users: gameUsers,
    } as RoomsModel;

    showDebugConsole('Sending welcome message');
    return await this.sendUserMessage({
      action: GameActionEnumerator.welcome,
      connectionId: connectionId,
      authorization: '',
      data: room,
    } as GameMessageDTO<RoomsModel>);
  }

  async sendUserEntered(users: GameUserWithUserEntity[]) {
    const usersToSend = this.getProcessedGameUsers(users);
    users.forEach(
      async (user) =>
        await this.sendUserMessage(<GameMessageDTO<GameUserWithUserEntity[]>>{
          action: GameActionEnumerator.userEntered,
          connectionId: user.connectionId,
          authorization: '',
          data: usersToSend,
        }),
    );
  }

  async sendGameStarting(
    roomName: string,
    users: GameUserEntity[],
    timeInSeconds: number,
    skipChangeState = false,
  ) {
    if (!skipChangeState) {
      await this.setGameState(roomName, GameStateEnumerator.starting);
    }

    await this.createChangeGameStateEvent({
      round: 1,
      state: GameStateEnumerator.answering,
      roomName: roomName,
      timeInSeconds: timeInSeconds,
    });

    users.forEach((user) =>
      this.sendUserMessage(<GameMessageDTO<any>>{
        action: GameActionEnumerator.gameStarting,
        connectionId: user.connectionId,
        authorization: '',
        data: {},
      }),
    );
  }

  async sendStartRound(
    roomName: string,
    users: GameUserEntity[],
    skipChangeState?: boolean,
  ) {
    if (!skipChangeState) {
      await this.setGameState(roomName, GameStateEnumerator.answering);
    }

    users.forEach((user) => {
      this.sendUserMessage(<GameMessageDTO<any>>{
        action: GameActionEnumerator.startRound,
        authorization: '',
        connectionId: user.connectionId,
        data: {},
      });
    });
  }

  async sendReviewRound(
    roomName: string,
    users: GameUserEntity[],
    skipChangeState = false,
  ) {
    if (!skipChangeState) {
      await this.setGameState(roomName, GameStateEnumerator.reviewing);
    }

    users.forEach(
      async (user) =>
        await this.sendUserMessage(<GameMessageDTO<any>>{
          action: GameActionEnumerator.reviewRound,
          authorization: '',
          connectionId: user.connectionId,
          data: {},
        }),
    );
  }

  async createChangeGameStateEvent({
    roomName,
    timeInSeconds,
    state,
    round,
  }: {
    roomName: string;
    timeInSeconds: number;
    state: GameStateEnumerator;
    round: number;
  }) {
    showDebugConsole(
      `creating change game state event: ${JSON.stringify({
        round: round,
        state: state,
        roomName,
        timeInSeconds,
      })}`,
    );
    const eventData: GameMessageDTO<any> = {
      action: GameActionEnumerator.changeState,
      authorization: process.env.EXTERNAL_API_TOKEN,
      connectionId: '',
      data: <ChangeGameStateDTO>{
        roomName: roomName,
        round: round,
        state: state,
        eventId: '',
      },
    };
    return await this.createGameEvent(timeInSeconds, roomName, eventData);
  }

  async createCancelGameEvent({
    roomName,
    timeInSeconds,
  }: {
    roomName: string;
    timeInSeconds: number;
  }) {
    const eventData: GameMessageDTO<string> = {
      action: GameActionEnumerator.cancelGame,
      authorization: process.env.EXTERNAL_API_TOKEN,
      connectionId: '',
      data: roomName,
    };
    return await this.createGameEvent(timeInSeconds, roomName, eventData);
  }

  async closeRoom(roomName: string) {
    const gameData = await this.getGameByRoomName(roomName);
    gameData.state = GameStateEnumerator.closed;
    gameData.status = 'CLOSED';
    return await this.updateGame(gameData);
  }

  async sendRoomDataToQueue(roomData: RoomsEntity) {
    if (process.env.USE_QUEUE && process.env.USE_QUEUE == 'true') {
      await this.commandBus.execute(new SendDataToQueueCommand(roomData));
    }
  }

  async sendEndGame(
    gameData: GameWithGameUsersWithUserEntity,
    skipChangeState = false,
  ) {
    if (!skipChangeState) {
      await this.setGameState(gameData.name, GameStateEnumerator.endGameReview);
    }
    const users = this.getProcessedGameUsers(gameData.game_users);
    const room = {
      ...gameData.data,
      users: users,
    } as RoomsModel;

    const mappedRoom = await mapRoomModelToRoomEntity(room);
    await this.sendRoomDataToQueue(mappedRoom);
    let endGameData: EndGameDataModel | null = null;
    const sendToApi =
      process.env.USE_SAVE_ROOM_ON_API &&
      process.env.USE_SAVE_ROOM_ON_API == 'true';
    if (sendToApi) {
      endGameData = await this.roomsService.saveRoomData({
        params: mappedRoom,
      });
    }

    if (sendToApi && endGameData == null) {
      await this.sendGameCancelled(gameData.game_users);
    } else {
      gameData.game_users.forEach((user) => {
        this.sendUserMessage(<GameMessageDTO<RoomsModel>>{
          action: GameActionEnumerator.endGame,
          connectionId: user.connectionId,
          authorization: '',
          data: room,
        });
        this.sendUserMessage(<GameMessageDTO<EndGameDataModel | null>>{
          action: GameActionEnumerator.endGameUpdateData,
          connectionId: user.connectionId,
          authorization: '',
          data: endGameData,
        });
      });
    }
    return await this.closeRoom(gameData.name);
  }

  async userEnteringOrBackingToTheGame(
    gameData: GameWithGameUsersWithUserEntity,
    userGameInfo: GameDataDTO<PlayerEnteringGameDTO>,
  ): Promise<GameMessageDTO<any>> {
    if (gameData.state == GameStateEnumerator.closed) {
      return await this.sendFailMessage(userGameInfo.connectionId, {
        code: GameErrorEnumerator.roomClosed,
        message: '',
      } as GameFailureDTO);
    }

    const userIndex = gameData.game_users.findIndex(
      (gameUser) =>
        gameUser.connectionId === userGameInfo.connectionId ||
        Number(gameUser.userId) == userGameInfo.data.userData.id,
    );

    if (userIndex == -1 && gameData.game_users.length >= gameData.max_users) {
      return await this.sendFailMessage(userGameInfo.connectionId, {
        code: GameErrorEnumerator.roomIsFull,
        message: '',
      } as GameFailureDTO);
    }

    const gameUserParams = {
      isRoomOwner: false,
      gameUserId: Number(userGameInfo.data.userData.id),
      data: userGameInfo,
    };

    await this.addOrUpdateUser(gameUserParams);
    // const newUser = await this.gameUsersService.getUserByConnectionId(
    //   userGameInfo.connectionId,
    // );

    const completeGameData = await this.getGameByRoomName(gameData.name, true);

    // if (userIndex > -1) {
    //   gameData.game_users[userIndex] = newUser;
    // } else {
    //   gameData.game_users.push(newUser);

    await this.sendUserEntered(completeGameData.game_users);

    return await this.sendWelcome(userGameInfo.connectionId, completeGameData);
  }

  async initiate(params: GameDataDTO<PlayerEnteringGameDTO>) {
    let gameData: GameWithGameUsersWithUserEntity;
    if (params.data.roomName != '') {
      gameData = await this.getGameByRoomName(params.data.roomName);
    }
    if (gameData) {
      return await this.userEnteringOrBackingToTheGame(gameData, params);
    }
    showDebugConsole('initiate without roomName');

    return await this.newUserEntering(params);
  }

  async createAddBotEvent(data: AddBotDTO) {
    const eventData: GameMessageDTO<AddBotDTO> = {
      action: GameActionEnumerator.addBot,
      authorization: process.env.EXTERNAL_API_TOKEN,
      connectionId: '',
      data: data,
    };
    const timeInSecondsToWait = Math.floor(Math.random() * 3) + 3;
    return await this.createGameEvent(
      timeInSecondsToWait,
      data.roomName,
      eventData,
    );
  }

  async addBot(params: GameDataDTO<AddBotDTO>) {
    if (params.data.roomName == '') {
      return;
    }
    const gameData = await this.getGameByRoomName(params.data.roomName, true);
    if (
      !gameData ||
      gameData.state != GameStateEnumerator.waitingPlayers ||
      gameData.game_users.length == gameData.max_users
    ) {
      return;
    }

    const newBot = this.botsService.selectBots(
      params.data.roomName,
      params.data.quizId,
      gameData.game_users.map((gameUser) => gameUser.user.identifier),
    );
    const gameDataDTO = <GameDataDTO<PlayerEnteringGameDTO>>{
      connectionId: '',
      token: '',
      data: {
        gameMode: gameData.game_mode,
        quizId: gameData.quiz_id,
        roomName: gameData.name,
        userData: newBot.user as unknown as UsersModel,
        isBot: true,
      },
    };

    await this.addOrUpdateUser({
      isRoomOwner: false,
      data: gameDataDTO,
    });

    const completeGameData = await this.getGameByRoomName(
      params.data.roomName,
      true,
    );
    await this.sendUserEntered(completeGameData.game_users);
    if (completeGameData.game_users.length < completeGameData.max_users) {
      return await this.createAddBotEvent({
        roomName: completeGameData.name,
        quizId: completeGameData.quiz_id,
      });
    }

    return await this.sendGameStarting(
      completeGameData.name,
      completeGameData.game_users,
      completeGameData.data.timeWaitingStartingGame,
    );
  }

  async getCurrentData(params: GameDataDTO<PlayerEnteringGameDTO>) {
    console.log(params);
  }

  async cancelGame(params: GameDataDTO<string>) {
    if (params.data == '') {
      return;
    }
    const gameData = await this.getGameByRoomName(params.data);
    if (!gameData || gameData.state != GameStateEnumerator.waitingPlayers) {
      return;
    }
    const res = await this.setGameState(
      params.data,
      GameStateEnumerator.cancelled,
    );

    if (!res) {
      return;
    }

    await this.sendGameCancelled(gameData.game_users);
    return await this.cancelEventsByRoomName(gameData.name);
  }

  async startGame(params: GameDataDTO<StartGameDTO>) {
    if (params.data.roomName == '' || params.connectionId == '') {
      return;
    }

    const gameData = await this.getGameByRoomName(params.data.roomName);

    const gameUser = await this.gameUsersService.getUserByConnectionId(
      params.connectionId,
    );

    if (
      !gameData ||
      (gameData.game_mode != GameModeEnumerator.oneOnInvitedPlayers &&
        gameData.game_mode != 'spotMatch') ||
      [(GameStateEnumerator.closed, GameStateEnumerator.cancelled)].includes(
        gameData.state as GameStateEnumerator,
      ) ||
      gameData.name != gameUser.roomName ||
      !gameUser.roomOwner
    ) {
      return;
    }

    return await this.sendGameStarting(
      gameData.name,
      gameData.game_users,
      gameData.data.timeWaitingStartingGame,
    );
  }

  async changeGameState(params: GameDataDTO<ChangeGameStateDTO>) {
    // if (params.data.eventId) {
    //   await this.gameEventsService.upsertGameEvent({
    //     eventId: params.data.eventId,
    //     roomName: params.data.roomName,
    //     isExecuted: true,
    //   });
    // }

    if (
      params.data.roomName == '' ||
      params.data.round < 1 ||
      !params.data.state
    ) {
      return;
    }

    const res = await this.setGameState(
      params.data.roomName,
      params.data.state,
      params.data.round,
    );

    if (!res) {
      return;
    }

    const gameData = await this.getGameByRoomName(params.data.roomName, true);

    switch (gameData.state) {
      // case GameStateEnumerator.starting:
      //   return await this.sendGameStarting(
      //     gameData.name,
      //     gameData.game_users,
      //     gameData.data.timeWaitingStartingGame,
      //   );
      case GameStateEnumerator.answering: {
        await this.sendStartRound(gameData.name, gameData.game_users, true);

        return await this.createChangeGameStateEvent({
          round: params.data.round,
          state: GameStateEnumerator.reviewing,
          roomName: gameData.name,
          timeInSeconds: gameData.data.timeAnsweringRound,
        });
      }
      case GameStateEnumerator.reviewing: {
        try {
          let state = GameStateEnumerator.answering;
          let round = params.data.round + 1;
          if (params.data.round == gameData.data.questions.length) {
            state = GameStateEnumerator.endGameReview;
            round = params.data.round;
          }
          await this.createChangeGameStateEvent({
            round: round,
            state: state,
            roomName: gameData.name,
            timeInSeconds: gameData.data.timeReviewingRound,
          });
          await this.sendBotsAnswers(gameData.name);
          await this.sendBotsPontuations(gameData.name);

          return await this.sendReviewRound(
            gameData.name,
            gameData.game_users,
            true,
          );
        } catch (err) {
          showDebugConsole('error changing game state: ' + err.message);
        }
      }
      case GameStateEnumerator.endGameReview: {
        return await this.sendEndGame(gameData, true);
      }
    }
  }

  async cancelEventsByRoomName(name: string) {
    // const events =
    //   await this.gameEventsService.getGameEventsByRoomNameAndStatus(
    //     name,
    //     false,
    //   );

    // return await this.gameEventsService.cancelEvents(
    //   events.map((event) => event.event_id),
    // );
    return this.schedulerService.cancelEventsByJobNameStartsWith(name);
  }

  async updateQuestions(
    gameUsers: GameUserWithUserEntity[],
    newAnswers: AnswersModel[],
  ) {
    gameUsers.forEach(async (gameUser) => {
      const message = {
        action: GameActionEnumerator.updateAnswers,
        authorization: '',
        connectionId: gameUser.connectionId,
        data: newAnswers,
      } as GameMessageDTO<AnswersModel[]>;
      await this.sendUserMessage(message);
    });
  }

  async playerReceivePontuation(
    gameUsers: GameUserWithUserEntity[],
    newPoints: PontuationModel,
  ) {
    const message = {
      action: GameActionEnumerator.playerReceivePontuation,
      authorization: '',
      connectionId: '',
      data: newPoints,
    } as GameMessageDTO<PontuationModel>;
    gameUsers.forEach(async (gameUser) => {
      message.connectionId = gameUser.connectionId;
      await this.sendUserMessage(message);
    });
  }

  async updatePontuations(
    gameUsers: GameUserWithUserEntity[],
    newPoints: PontuationModel[],
  ) {
    const message = {
      action: GameActionEnumerator.updatePontuations,
      authorization: '',
      connectionId: '',
      data: newPoints,
    } as GameMessageDTO<PontuationModel[]>;
    gameUsers.forEach(async (gameUser) => {
      message.connectionId = gameUser.connectionId;
      await this.sendUserMessage(message);
    });
  }

  async sendGameCancelled(users: GameUserEntity[]): Promise<void> {
    users.forEach(async (user) => {
      const message = {
        action: GameActionEnumerator.gameCancelled,
        connectionId: user.connectionId,
        authorization: '',
        data: null,
      } as GameMessageDTO<any>;
      await this.sendUserMessage(message);
    });
  }

  async sendAnswer(params: GameDataDTO<UserAnswerModel>) {
    try {
      const gameUser = await this.gameUsersService.getUserByConnectionId(
        params.connectionId,
      );
      const gameData = await this.getGameByRoomName(
        params.data.roomName,
        false,
      );
      if (
        !gameData ||
        params.data.round < gameData.round - 1 ||
        params.data.round > gameData.round
      ) {
        return await this.sendFailMessage(params.connectionId, {
          code: GameErrorEnumerator.roomDontAcceptingAnswers,
          message: '',
        } as GameFailureDTO);
      }
      const newAnswer = <AnswersModel>{
        currentRound: params.data.round,
        questionId: gameData.data.questions[params.data.round - 1].id,
        userIndentifier: gameUser.user.identifier,
        answer: params.data.answer,
        user_id: Number(gameUser.user.id),
        is_correct_answer: params.data.is_correct_answer,
        questions_options_id: params.data.question_option_id,
      };
      const index = gameData.data.answers.findIndex(
        (answer) =>
          answer.currentRound == params.data.round &&
          answer.user_id == Number(gameUser.userId),
      );
      if (index > -1) {
        gameData.data.answers[index] = newAnswer;
      } else {
        gameData.data.answers.push(newAnswer);
      }

      await this.updateGame(gameData);

      const completeGameData = await this.getGameByRoomName(gameData.name);

      this.updateQuestions(
        completeGameData.game_users,
        completeGameData.data.answers,
      );
      if (
        completeGameData.state == GameStateEnumerator.answering &&
        completeGameData.game_mode == GameModeEnumerator.singlePlayer
      ) {
        this.cancelEventsByRoomName(completeGameData.name);

        await this.sendReviewRound(gameData.name, completeGameData.game_users);

        if (completeGameData.round < completeGameData.data.questions.length) {
          await this.createChangeGameStateEvent({
            state: GameStateEnumerator.answering,
            timeInSeconds: gameData.data.timeReviewingRound,
            round: gameData.round + 1,
            roomName: gameData.name,
          });
        } else {
          await this.createChangeGameStateEvent({
            state: GameStateEnumerator.endGameReview,
            timeInSeconds: gameData.data.timeReviewingRound,
            round: gameData.round,
            roomName: gameData.name,
          });
        }
      }
    } catch (err) {}
  }
  async sendPontuation(params: GameDataDTO<PlayerGivingGamePointDTO>) {
    try {
      const gameUser = await this.gameUsersService.getUserByConnectionId(
        params.connectionId,
      );
      const gameData = await this.getGameByRoomName(
        params.data.roomName,
        false,
      );
      if (
        !gameData ||
        params.data.round < gameData.round - 1 ||
        params.data.round > gameData.round
      ) {
        return await this.sendFailMessage(params.connectionId, {
          code: GameErrorEnumerator.roomDontAcceptingPoints,
          message: '',
        } as GameFailureDTO);
      }

      const newPoints = {
        currentRound: params.data.round,
        userThatSetPontuationIdentifier:
          params.data.userThatSetPontuationIdentifier,
        userThatReceivePontuationIdentifier:
          params.data.userThatReceivePontuationIdentifier,
        pontuation:
          params.data.pontuation == GamePointTypeEnumerator.fire ? 10 : 0,
        questionId: gameData.data.questions[params.data.round - 1].id ?? 0,
      } as PontuationModel;
      const index = gameData.data.answers.findIndex(
        (answer) =>
          answer.currentRound == params.data.round &&
          answer.user_id == Number(gameUser.userId),
      );
      if (index > -1) {
        gameData.data.pontuations[index] = newPoints;
      } else {
        gameData.data.pontuations.push(newPoints);
      }

      await this.updateGame(gameData);

      const completeGameData = await this.getGameByRoomName(gameData.name);

      return await this.playerReceivePontuation(
        completeGameData.game_users,
        newPoints,
      );
      // return await this.updatePontuations(
      //   completeGameData.game_users,
      //   completeGameData.data.pontuations,
      // );
    } catch (err) {}
  }

  async onConnect(params: GameDataDTO<undefined>) {
    console.log(params);
    return;
  }
  async onDisconnect(params: GameDataDTO<undefined>) {
    showDebugConsole('disconnecting user: ' + params.connectionId);
    const gameUser = await this.gameUsersService.getUserByConnectionId(
      params.connectionId,
    );
    gameUser.isConnected = false;
    gameUser.connectionId = '';
    return await this.gameUsersService.upsertGameUser(gameUser);
  }
}
