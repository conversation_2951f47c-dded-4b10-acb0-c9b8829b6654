import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateEventCommand } from '../create-event.command';
import * as AWS from 'aws-sdk';
import { GameEventsService } from 'src/game-events/game-events.service';

@CommandHandler(CreateEventCommand)
export class CreateEventHandler implements ICommandHandler<CreateEventCommand> {
  constructor(private readonly gameEventsService: GameEventsService) {}

  async execute(command: CreateEventCommand): Promise<boolean> {
    try {
      const stepFunctions = new AWS.StepFunctions();

      const params = {
        stateMachineArn: process.env.AWS_STATE_MACHINE_ARN,
        input: JSON.stringify({
          waitTime: command.timeInSeconds,
          event: command.event,
        }),
      };

      const result = await stepFunctions.startExecution(params).promise();

      console.log('Started Step Function execution:', result.executionArn);

      await this.gameEventsService.upsertGameEvent({
        eventId: result.executionArn,
        roomName: command.roomName,
        isExecuted: false,
      });

      return true;
    } catch (error) {
      console.error('Failed to start Step Function execution:', error);
      return false;
    }
  }
}
