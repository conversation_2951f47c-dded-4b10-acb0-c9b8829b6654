import { Injectable } from '@nestjs/common';
import axios from 'axios';
import useUtils from 'src/shared/utils/use-utils';
import * as https from 'https';
import { RoomsEntity } from 'src/games/models/entities/rooms.entity';
import { EndGameDataModel } from './models/end-game-data.model';

@Injectable()
export class RoomsService {
  utils = useUtils();

  async saveRoomData({
    params,
  }: {
    params: RoomsEntity;
  }): Promise<EndGameDataModel | null> {
    try {
      const agent = new https.Agent({
        rejectUnauthorized: false, // Desabilita a verificação do certificado SSL
      });
      const response = await axios.post<EndGameDataModel>(
        `${process.env.EXTERNAL_API_URL}/rooms`,
        params,
        {
          httpsAgent: agent,
          headers: this.utils.getHeaders({}),
        },
      );
      if (response.status === 200 && response.data) {
        return response.data;
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }
}
