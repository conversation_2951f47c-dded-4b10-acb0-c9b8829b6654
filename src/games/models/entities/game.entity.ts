import { games, Prisma } from '@prisma/client';
import GameUserEntity, {
  GameUserWithUserEntity,
} from 'src/game-users/models/entities/game-user.entity';
import { RoomsModel } from '../../../rooms/models/rooms.model';

type GameEntity = Omit<games, 'data'> & { data: RoomsModel };

export type GameWithGameUsersEntity = GameEntity & {
  game_users: GameUserEntity[];
};

export type GameWithGameUsersWithUserEntity = GameEntity & {
  game_users: GameUserWithUserEntity[];
};

export default GameEntity;

export function gamesToGamesEntity<R>(data: games): R {
  let jsonData: unknown;

  if (typeof data.data === 'string') {
    try {
      jsonData = JSON.parse(data.data);
    } catch (error) {
      return null;
    }
  } else {
    jsonData = data.data;
  }

  if (typeof jsonData === 'object' && jsonData !== null) {
    const res = { ...data, data: jsonData as RoomsModel } as R;
    return res;
  }
  return null;
}

export function gameEntityToGame(data: GameEntity): games {
  return { ...data, data: data.data as unknown as Prisma.JsonValue } as games;
}
