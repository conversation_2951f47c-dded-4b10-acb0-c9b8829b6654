import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { queuesConst } from 'src/shared/constants/queues';
import { ProcessorsNamesEnumerator } from '../models/enumerators/processors-names.enumerator';
import GameMessageDTO from 'src/games/models/dtos/game-message.dto';
import GameActionEnumerator from 'src/games/models/enumerators/game-action.enumerator';
import { GamesService } from 'src/games/games.service';
import { createGameDataDTO } from 'src/games/models/dtos/game-data.dto';

@Processor(queuesConst.scheduler)
export class SchedulerProcessor extends WorkerHost {
  constructor(private readonly gamesService: GamesService) {
    super();
  }

  async process(job: Job<GameMessageDTO<any>>) {
    switch (job.name) {
      case ProcessorsNamesEnumerator.gameEvents: {
        await this.processGameEvents(job.data);
        break;
      }
    }
  }

  async processGameEvents(info: GameMessageDTO<any>) {
    const gameData = createGameDataDTO({
      connectionId: info.connectionId,
      token: info.authorization,
      data: info.data,
    });
    switch (info.action) {
      case GameActionEnumerator.cancelGame: {
        return await this.gamesService.cancelGame(gameData);
      }
      case GameActionEnumerator.changeState: {
        return await this.gamesService.changeGameState(gameData);
      }
      case GameActionEnumerator.addBot: {
        return await this.gamesService.addBot(gameData);
      }
    }
    return info;
  }
}
