export interface RoomsEntity {
  name: string;
  how_many_people: number;
  users: number[];
  answers: AnswersEntity[];
  pontuations: PontuationsEntity[];
  gameMode: string;
}

export interface PontuationsEntity {
  round: number;
  user_give_id: number;
  user_id: number;
  point_type_id: number; // fire = 1; ice = 2
  question_id: number;
}

export interface AnswersEntity {
  question_id: number;
  user_id: number;
  value: string;
  questions_options_id?: string;
}
