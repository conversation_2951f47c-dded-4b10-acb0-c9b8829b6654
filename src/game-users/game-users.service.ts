import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import GameUserEntity, {
  GameUserWithUserEntity,
} from './models/entities/game-user.entity';
import { CommandBus } from '@nestjs/cqrs';
import { UpsertCommand } from './commands/upsert.command';
import { CreateCommand } from './commands/create.command';
import GameUserModel from './models/game-user.model';

@Injectable()
export class GameUsersService {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly prismaService: PrismaService,
  ) {}

  public async getUsersFromRoom(roomName: string) {
    this.prismaService.game_users.findMany({
      where: {
        roomName: roomName,
      },
    });
  }

  public async getUserByConnectionId(
    connectionId: string,
  ): Promise<GameUserWithUserEntity | null> {
    return this.prismaService.game_users.findFirst({
      where: {
        connectionId,
      },
      include: { user: true },
    });
  }

  public async addNewUser(data: GameUserModel): Promise<GameUserEntity> {
    return await this.commandBus.execute(new CreateCommand(data));
  }

  async upsertGameUser(data: GameUserEntity): Promise<GameUserEntity> {
    return await this.commandBus.execute(new UpsertCommand(data));
  }
}
