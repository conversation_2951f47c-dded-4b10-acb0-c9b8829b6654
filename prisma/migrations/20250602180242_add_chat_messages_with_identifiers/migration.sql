-- CreateTable
CREATE TABLE `chat_messages` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `sender_identifier` VARCHAR(255) NOT NULL,
    `receiver_identifier` VARCHAR(255) NOT NULL,
    `content` TEXT NOT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `is_read` BOOLEAN NOT NULL DEFAULT false,

    INDEX `chat_messages_sender_identifier_idx`(`sender_identifier`),
    INDEX `chat_messages_receiver_identifier_idx`(`receiver_identifier`),
    INDEX `chat_messages_created_at_idx`(`created_at`),
    INDEX `chat_messages_is_read_idx`(`is_read`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `chat_messages` ADD CONSTRAINT `chat_messages_sender_identifier_fkey` <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`sender_identifier`) REFERENCES `users`(`identifier`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `chat_messages` ADD CONSTRAINT `chat_messages_receiver_identifier_fkey` FOREIGN KEY (`receiver_identifier`) REFERENCES `users`(`identifier`) ON DELETE CASCADE ON UPDATE CASCADE;
