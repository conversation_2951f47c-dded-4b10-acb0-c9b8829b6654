## Description

Game Api repository.

## Installation

```bash
$ yarn install
```

## Running the app

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

## Test

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```


## Docker

```bash
# build and run
$ ./docker-build-and-run.sh

# build
$ ./docker-build.sh

# run
$ ./docker-run.sh
```