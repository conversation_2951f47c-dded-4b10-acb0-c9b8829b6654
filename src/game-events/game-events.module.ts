import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'src/prisma/prisma.module';
import { CqrsModule } from '@nestjs/cqrs';
import { GameEventsService } from './game-events.service';
import { GameEventsController } from './game-events.controller';
import { UpsertEventHandler } from './commands/handlers/upsert-event.handler';
import { CancelEventHandler } from './commands/handlers/cancel-event.handler';

const commands = [CancelEventHandler, UpsertEventHandler];

@Module({
  providers: [GameEventsService, ...commands],
  exports: [GameEventsService],
  imports: [PrismaModule, CqrsModule],
  controllers: [GameEventsController],
})
export class GameEventsModule {}
