export default interface GameConfigParamsModel {
  timeWaitingStartingGame: number;
  maxRoundsInGame: number;
  timeAnsweringRound: number;
  timeReviewingRound: number;
  gameQuestionsSequence: string;
  gameRounds: number;
  devGameQuestionsSequence: string;
  timeAnsweringQuestionDirect: number;
  timeAnsweringQuestionMultiple: number;
  isPublicationMode: boolean;
  timeWaitingStartGameMultiplayer: number;
  timeWaitingStartGameSingleplayer: number;
  timeAnsweringRoundSingleplayer: number;
  timeReviewingRoundSingleplayer: number;
}
