import { AnswersModel } from '../../questions/models/answers.model';
import { QuestionsModel } from '../../questions/models/questions.model';
import { GameUserWithUserEntity } from 'src/game-users/models/entities/game-user.entity';
import GameStateEnumerator from '../../games/models/enumerators/game-state.enumerator';
import GameModeEnumerator from '../../games/models/enumerators/game-mode.enumerator';
import {
  PontuationsEntity,
  RoomsEntity,
} from '../../games/models/entities/rooms.entity';
import PontuationModel from 'src/games/models/pontuations.model';
import { GamePointTypeEnumerator } from 'src/games/models/enumerators/game-point-type.enumerator';

export interface RoomsModel {
  roomName: string;
  timeWaitingStartingGame: number;
  timeAnsweringRound: number; // deprecated
  timeReviewingRound: number;
  stateTimeInSeconds: number;
  timeAnsweringQuestionDirect: number;
  timeAnsweringQuestionMultiple: number;
  users: GameUserWithUserEntity[];
  numRounds: number;
  flagMaxPlayers: number;
  flagPlayersReady: number;
  flagCurrentRound: number;
  flagPlayersFinished: number;
  questions: QuestionsModel[];
  answers: AnswersModel[];
  pontuations: PontuationModel[];
  gameState: GameStateEnumerator;
  gameMode: GameModeEnumerator;
  lastStatusChange: number;
  quiz_id: number;
}

export async function mapRoomModelToRoomEntity(
  roomData: RoomsModel,
): Promise<RoomsEntity> {
  const res = <RoomsEntity>(<unknown>{
    name: roomData.roomName,
    how_many_people: roomData.users.length,
    quiz_id: roomData.quiz_id,
    users: roomData.users.map((userData) => Number(userData.user.id)),
    answers: roomData.answers.map((answer) => ({
      question_id: Number(answer.questionId),
      user_id: Number(answer.user_id),
      value: answer.answer,
      questions_options_id: answer.questions_options_id,
    })),
    pontuations:
      (await mapPontuations(roomData.pontuations, roomData.users)) ?? [],
    gameMode: roomData.gameMode,
  });
  return res;
}

async function mapPontuations(
  pontuations: PontuationModel[] | undefined,
  gameUsers: GameUserWithUserEntity[],
): Promise<PontuationsEntity[]> {
  const getUserByIdentifier = (identifier: string) => {
    return gameUsers.find(
      (gameUser) => gameUser.user.identifier === identifier,
    );
  };
  return new Promise(async (resolve, reject) => {
    if (pontuations) {
      const res = <PontuationsEntity[]>[];
      for (let i = 0; i < pontuations.length; i++) {
        const user_id = getUserByIdentifier(
          pontuations[i].userThatReceivePontuationIdentifier,
        )?.user?.id;

        const user_give_id = getUserByIdentifier(
          pontuations[i].userThatSetPontuationIdentifier,
        )?.user?.id;

        res.push(<PontuationsEntity>{
          round: pontuations[i].currentRound,
          user_give_id: Number(user_give_id),
          user_id: Number(user_id),
          point_type_id:
            pontuations[i].pontuation == GamePointTypeEnumerator.fire ? 1 : 2,
          question_id: Number(pontuations[i].questionId),
        });
      }
      resolve(<PontuationsEntity[]>(<unknown>res));
    }
    reject(undefined);
  });
}
