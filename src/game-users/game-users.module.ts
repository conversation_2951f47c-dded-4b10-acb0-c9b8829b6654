import { <PERSON>du<PERSON> } from '@nestjs/common';
import { GameUsersService } from './game-users.service';
import { GameUsersController } from './game-users.controller';
import { CreateHandler } from './commands/handlers/create.handler';
import { UpsertHandler } from './commands/handlers/upsert.handler';
import { CqrsModule } from '@nestjs/cqrs';
import { PrismaModule } from 'src/prisma/prisma.module';

const CommandHandlers = [CreateHand<PERSON>, UpsertHandler];

@Module({
  providers: [GameUsersService, ...CommandHandlers],
  imports: [PrismaModule, CqrsModule],
  controllers: [GameUsersController],
  exports: [GameUsersService],
})
export class GameUsersModule {}
