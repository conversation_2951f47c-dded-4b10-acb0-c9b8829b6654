export interface EndGameDataModel {
  name: string;
  id: string;
  how_many_people: number;
  question_time_in_seconds: number;
  response_time_in_seconds: number;
  quiz_id: number;
  created_at: Date;
  updated_at: Date;
  results: EndGamePlayerGameResult[];
}

export interface EndGamePlayerGameResult {
  user_id: number;
  correct: number;
  wrong: number;
  points: number;
  total: number;
  progress: number;
}
