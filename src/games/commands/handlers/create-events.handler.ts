import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateEventsCommand } from '../create-events.command';
import * as AWS from 'aws-sdk';

@CommandHandler(CreateEventsCommand)
export class CreateEventsHandler
  implements ICommandHandler<CreateEventsCommand>
{
  async execute(command: CreateEventsCommand): Promise<boolean> {
    const eventBridge = new AWS.EventBridge({
      region: process.env.AWS_REGION,
    });

    await eventBridge
      .putEvents({
        Entries:
          command.events as unknown as AWS.EventBridge.PutEventsRequestEntryList,
      })
      .promise();
    return true;
  }
}
