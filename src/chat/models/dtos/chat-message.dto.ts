import ChatActionEnumerator from '../enumerators/chat-action.enumerator';

export default interface ChatMessageDTO<T> {
  action: ChatActionEnumerator;
  authorization: string;
  connectionId: string;
  data: T;
}

export interface ChatDataDTO<T> {
  connectionId: string;
  token: string;
  data: T;
}

export function createChatDataDTO({
  connectionId,
  token,
  data,
}: ChatDataDTO<any>): ChatDataDTO<any> {
  return <ChatDataDTO<any>>{
    connectionId,
    token,
    data,
  };
}
