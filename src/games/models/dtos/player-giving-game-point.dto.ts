import { GamePointTypeEnumerator } from '../enumerators/game-point-type.enumerator';

export default interface PlayerGivingGamePointDTO {
  roomName: string;
  round: number;
  userThatSetPontuationIdentifier: string;
  userThatReceivePontuationIdentifier: string;
  pontuation: GamePointTypeEnumerator;
  questionId?: number;
}

export function isInstanceOfPlayerGivingGamePointDTO(
  object: any,
): object is PlayerGivingGamePointDTO {
  return object?.hasOwnProperty('gameMode');
}
