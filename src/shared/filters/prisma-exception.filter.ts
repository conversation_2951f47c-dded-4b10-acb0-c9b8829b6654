import { ExceptionFilter, Catch, ArgumentsHost } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Request, Response } from 'express';
import * as Sentry from '@sentry/node';
import { jwtDecode } from 'jwt-decode';
import { JwtPayload } from '../types/jwt-payload.type';

@Catch(
  Prisma.PrismaClientKnownRequestError,
  Prisma.PrismaClientUnknownRequestError,
  Prisma.PrismaClientValidationError,
)
export class PrismaExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = 400;

    let userId;
    let message = exception.message;

    if (request.headers.authorization) {
      const decoded = jwtDecode(request.headers.authorization) as JwtPayload;
      userId = decoded?.sub;
    }

    Sentry.captureException(exception, {
      extra: {
        url: request.url,
        method: request.method,
        body: request.body,
        query: request.query,
        params: request.params,
        userId,
      },
    });

    if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      if (exception.code === 'P2002') {
        message = message?.replace(
          'Unique constraint failed on the fields',
          'Já existe um registro com mesmo valor para os campos',
        );
      }
      return response.status(status).json({
        statusCode: status,
        error: message,
        message: message,
      });
    }

    if (exception instanceof Prisma.PrismaClientValidationError) {
      return response.status(status).json({
        statusCode: status,
        error: message,
        message: message,
      });
    }
  }
}
