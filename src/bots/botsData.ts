import * as fs from 'fs';
import * as path from 'path';
import { GameUserWithUserEntity } from 'src/game-users/models/entities/game-user.entity';

function readJsonFile<T>(filePath: string, defaultResponse: T): T {
  try {
    const absolutePath = path.resolve(filePath);
    const fileContents = fs.readFileSync(absolutePath, 'utf-8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error(`Erro ao ler o arquivo ${filePath}:`, error);
    return defaultResponse;
  }
}

export const botsIdentifiers = readJsonFile<string[]>(
  'assets/bots/bots_identifiers.json',
  [],
);

export const botsList = readJsonFile<GameUserWithUserEntity[]>(
  'assets/bots/bots.json',
  [],
);
