import { forwardRef, Module } from '@nestjs/common';
import { GamesService } from './games.service';
import { GamesController } from './games.controller';
import { PrismaModule } from 'src/prisma/prisma.module';
import { AddNewGameUserHandler } from './commands/handlers/add-new-game-user.handler';
import { AddUserToGameRoomHandler } from './commands/handlers/add-user-to-game-room.handler';
import { CreateNewGameRoomHandler } from './commands/handlers/create-new-game-room.handler';
import { DeleteGameRoomHandler } from './commands/handlers/delete-game-room.handler';
import { SendMessageToGameUsersHandler } from './commands/handlers/send-message-to-game-users.handler';
import { UpdateGameRoomHandler } from './commands/handlers/update-game-room.handler';
import { UpdateGameUserHandler } from './commands/handlers/update-game-user.handler';
import { GameUsersModule } from 'src/game-users/game-users.module';
import { CqrsModule } from '@nestjs/cqrs';
import { QuestionsModule } from 'src/questions/questions.module';
import { UpdateGameHandler } from './commands/handlers/update-game.handler';
import { CreateEventHandler } from './commands/handlers/create-event.handler';
import { CreateEventsHandler } from './commands/handlers/create-events.handler';
import { SendDataToQueueHandler } from './commands/handlers/send-data-to-queue.handler';
import { GameEventsModule } from 'src/game-events/game-events.module';
import { BotsModule } from 'src/bots/bots.module';
import { RoomsModule } from 'src/rooms/rooms.module';
import { SchedulerEventHandler } from './commands/handlers/schedule-event.handler';
import { SchedulerModule } from 'src/scheduler/scheduler.module';

const CommandHandlers = [
  AddUserToGameRoomHandler,
  AddNewGameUserHandler,
  DeleteGameRoomHandler,
  CreateNewGameRoomHandler,
  SendMessageToGameUsersHandler,
  UpdateGameRoomHandler,
  UpdateGameUserHandler,
  UpdateGameHandler,
  CreateEventHandler,
  CreateEventsHandler,
  SendDataToQueueHandler,
  SchedulerEventHandler,
];

@Module({
  imports: [
    PrismaModule,
    CqrsModule,
    GameUsersModule,
    QuestionsModule,
    GameEventsModule,
    BotsModule,
    RoomsModule,
    forwardRef(() => SchedulerModule),
  ],
  providers: [GamesService, ...CommandHandlers],
  exports: [GamesService],
  controllers: [GamesController],
})
export class GamesModule {}
