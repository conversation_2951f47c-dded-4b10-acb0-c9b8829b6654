import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './chat.service';
import { PrismaService } from '../prisma/prisma.service';
import { PushNotificationService } from './services/push-notification.service';
import { CommandBus } from '@nestjs/cqrs';

describe('ChatService', () => {
  let service: ChatService;
  let prismaService: PrismaService;
  let pushNotificationService: PushNotificationService;
  let commandBus: CommandBus;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        {
          provide: PrismaService,
          useValue: {
            chat_messages: {
              create: jest.fn(),
              findMany: jest.fn(),
              count: jest.fn(),
              updateMany: jest.fn(),
            },
            users: {
              findUnique: jest.fn(),
            },
            game_users: {
              findFirst: jest.fn(),
            },
            $queryRaw: jest.fn(),
          },
        },
        {
          provide: PushNotificationService,
          useValue: {
            sendChatNotification: jest.fn(),
          },
        },
        {
          provide: CommandBus,
          useValue: {
            execute: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);
    prismaService = module.get<PrismaService>(PrismaService);
    pushNotificationService = module.get<PushNotificationService>(PushNotificationService);
    commandBus = module.get<CommandBus>(CommandBus);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendMessage', () => {
    it('should send a message successfully', async () => {
      const mockSender = {
        id: BigInt(1),
        name: 'John',
        avatar: 'avatar1.jpg',
        uuid: 'uuid1',
        email: '<EMAIL>',
        phone_number: '123456789',
        identifier: 'john123',
        email_verified_at: new Date(),
        password: 'hashedpassword',
        remember_token: null,
        created_at: new Date(),
        updated_at: new Date(),
        google_id: null,
        apple_id: null,
        onesignal_id: null,
        lastaccess_at: new Date()
      };

      const mockReceiver = {
        id: BigInt(2),
        name: 'Jane',
        avatar: 'avatar2.jpg',
        uuid: 'uuid2',
        email: '<EMAIL>',
        phone_number: '987654321',
        identifier: 'jane123',
        email_verified_at: new Date(),
        password: 'hashedpassword',
        remember_token: null,
        created_at: new Date(),
        updated_at: new Date(),
        google_id: null,
        apple_id: null,
        onesignal_id: null,
        lastaccess_at: new Date()
      };

      const mockMessage = {
        id: 1,
        senderId: BigInt(1),
        receiverId: BigInt(2),
        content: 'Hello!',
        created_at: new Date(),
        is_read: false,
        sender: mockSender,
        receiver: mockReceiver,
      };

      jest.spyOn(prismaService.users, 'findUnique')
        .mockResolvedValueOnce(mockSender)
        .mockResolvedValueOnce(mockReceiver);

      jest.spyOn(prismaService.chat_messages, 'create').mockResolvedValue(mockMessage as any);
      jest.spyOn(service as any, 'getUserConnectionId').mockResolvedValue(null);
      jest.spyOn(pushNotificationService, 'sendChatNotification').mockResolvedValue(true);

      const result = await service.sendMessage('user1', {
        receiverIdentifier: 'user2',
        content: 'Hello!',
      });

      expect(result).toBeDefined();
      expect(result.content).toBe('Hello!');
      expect(pushNotificationService.sendChatNotification).toHaveBeenCalled();
    });
  });
});
