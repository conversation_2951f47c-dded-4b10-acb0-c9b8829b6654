import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  NotFoundException,
  UnauthorizedException,
  HttpException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import * as Sentry from '@sentry/node';
import { jwtDecode } from 'jwt-decode';
import { JwtPayload } from '../types/jwt-payload.type';
import showDebugConsole from '../utils/show-debug-console';

@Catch()
export class AllExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    showDebugConsole('new exception: ' + exception);
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    let status = 400;
    const request = ctx.getRequest<Request>();

    if (exception instanceof HttpException) {
      status = exception.getStatus();

      if (
        exception instanceof NotFoundException ||
        exception instanceof UnauthorizedException
      ) {
        return response.status(status).json({
          statusCode: status,
          error: exception.message,
          message: exception.message,
        });
      }

      let userId;

      if (request.headers.authorization) {
        const decoded = jwtDecode(request.headers.authorization) as JwtPayload;
        userId = decoded.sub;
      }

      Sentry.captureException(exception, {
        extra: {
          url: request.url,
          method: request.method,
          body: request.body,
          query: request.query,
          params: request.params,
          userId,
        },
      });

      return response.status(status).json({
        statusCode: status,
        error: exception.message,
        message: (exception as any).response.message || exception.message,
      });
    } else if (!!response) {
      Sentry.captureException(exception);
      return response.status(status).json({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Por favor, tente novamente mais tarde.',
      });
    } else {
      Sentry.captureException(exception);
    }
  }
}
