import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { UpdateGameCommand } from '../update-game.command';
import { PrismaService } from 'src/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import GameEntity, {
  gamesToGamesEntity,
} from 'src/games/models/entities/game.entity';

@CommandHandler(UpdateGameCommand)
export class UpdateGameHandler implements ICommandHandler<UpdateGameCommand> {
  constructor(private readonly prismaService: PrismaService) {}
  async execute(command: UpdateGameCommand): Promise<GameEntity> {
    const updatedGame = await this.prismaService.games.update({
      where: {
        id: command.game.id,
      },
      data: {
        name: command.game.name,
        round: command.game.round,
        state: command.game.state,
        status: command.game.status,
        quiz_id: command.game.quiz_id,
        max_users: command.game.max_users,
        data: command.game.data as unknown as Prisma.InputJsonValue,
      },
    });
    return gamesToGamesEntity<GameEntity>(updatedGame);
  }
}
