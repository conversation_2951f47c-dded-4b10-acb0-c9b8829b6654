import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'src/prisma/prisma.service';
import { UpsertEventCommand } from '../upsert-event.command';
import GameEventEntity from 'src/game-events/model/entities/game-event.entity';

@CommandHandler(UpsertEventCommand)
export class UpsertEventHandler implements ICommandHandler<UpsertEventCommand> {
  constructor(private readonly prismaService: PrismaService) {}
  async execute(command: UpsertEventCommand): Promise<GameEventEntity> {
    return await this.prismaService.game_events.upsert({
      where: {
        event_id: command.gameEvent.eventId,
      },
      create: {
        event_id: command.gameEvent.eventId,
        room_name: command.gameEvent.roomName,
        is_executed: command.gameEvent.isExecuted,
      },
      update: {
        is_executed: command.gameEvent.isExecuted,
      },
    });
  }
}
